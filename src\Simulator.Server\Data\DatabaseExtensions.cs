using Microsoft.EntityFrameworkCore;

namespace Simulator.Server.Data
{
    public static class DatabaseExtensions
    {
        /// <summary>
        /// 确保数据库已创建并应用所有迁移
        /// </summary>
        /// <param name="app"></param>
        /// <returns></returns>
        public static async Task<IApplicationBuilder> EnsureDatabaseCreatedAsync(this IApplicationBuilder app)
        {
            using var scope = app.ApplicationServices.CreateScope();
            var context = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();
            
            // 应用所有待处理的迁移
            await context.Database.MigrateAsync();
            
            return app;
        }

        /// <summary>
        /// 种子数据（可选）
        /// </summary>
        /// <param name="app"></param>
        /// <returns></returns>
        public static async Task<IApplicationBuilder> SeedDatabaseAsync(this IApplicationBuilder app)
        {
            using var scope = app.ApplicationServices.CreateScope();
            var context = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();
            
            // 检查是否已有数据
            if (!await context.Users.AnyAsync())
            {
                // 添加示例数据
                var sampleUsers = new[]
                {
                    new Models.User { Name = "张三", Email = "<EMAIL>" },
                    new Models.User { Name = "李四", Email = "<EMAIL>" },
                    new Models.User { Name = "王五", Email = "<EMAIL>" }
                };

                context.Users.AddRange(sampleUsers);
                await context.SaveChangesAsync();
            }
            
            return app;
        }
    }
}
