﻿using Mapster;
using Microsoft.EntityFrameworkCore;
using Simulator.Server.Data;

namespace Simulator.Server
{
    public static class Startup
    {
        public static void ConfigureServices(IServiceCollection services, IConfiguration configuration)
        {
            // 配置 Entity Framework Core
            services.AddDbContext<ApplicationDbContext>(options =>
                options.UseSqlite(configuration.GetConnectionString("DefaultConnection")));

            services.AddMapster();
        }
    }
}