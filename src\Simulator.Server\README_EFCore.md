# Entity Framework Core 集成说明

本项目已成功集成 Entity Framework Core 并配置使用 SQLite 数据库。

## 已安装的包

- `Microsoft.EntityFrameworkCore.Sqlite` - SQLite 数据库提供程序
- `Microsoft.EntityFrameworkCore.Tools` - EF Core 工具（用于迁移等）

## 项目结构

```
src/Simulator.Server/
├── Data/
│   ├── ApplicationDbContext.cs      # 数据库上下文
│   └── DatabaseExtensions.cs       # 数据库扩展方法
├── Models/
│   └── User.cs                      # 示例实体模型
├── Controllers/
│   └── UsersController.cs           # 示例 API 控制器
├── Migrations/                      # EF Core 迁移文件
├── appsettings.json                 # 生产环境配置
├── appsettings.Development.json     # 开发环境配置
└── simulator_dev.db                 # SQLite 数据库文件（开发环境）
```

## 数据库配置

### 连接字符串

- **开发环境**: `Data Source=simulator_dev.db`
- **生产环境**: `Data Source=simulator.db`

### 数据库上下文

`ApplicationDbContext` 类位于 `Data/ApplicationDbContext.cs`，包含：
- 实体配置
- 关系映射
- 约束定义

## 使用示例

### 1. 添加新实体

1. 在 `Models/` 目录下创建实体类
2. 在 `ApplicationDbContext` 中添加 `DbSet<T>` 属性
3. 在 `OnModelCreating` 方法中配置实体关系
4. 创建迁移：`dotnet ef migrations add <MigrationName>`
5. 应用迁移：`dotnet ef database update`

### 2. 在控制器中使用

```csharp
[ApiController]
[Route("api/[controller]")]
public class YourController : ControllerBase
{
    private readonly ApplicationDbContext _context;

    public YourController(ApplicationDbContext context)
    {
        _context = context;
    }

    [HttpGet]
    public async Task<ActionResult<IEnumerable<YourEntity>>> Get()
    {
        return await _context.YourEntities.ToListAsync();
    }
}
```

### 3. 在服务中使用

```csharp
public class YourService
{
    private readonly ApplicationDbContext _context;

    public YourService(ApplicationDbContext context)
    {
        _context = context;
    }

    public async Task<List<YourEntity>> GetAllAsync()
    {
        return await _context.YourEntities.ToListAsync();
    }
}
```

## 常用 EF Core 命令

```bash
# 创建迁移
dotnet ef migrations add <MigrationName>

# 应用迁移
dotnet ef database update

# 回滚到指定迁移
dotnet ef database update <MigrationName>

# 删除最后一个迁移
dotnet ef migrations remove

# 查看迁移状态
dotnet ef migrations list

# 生成 SQL 脚本
dotnet ef migrations script
```

## API 端点示例

应用程序包含一个示例 Users API：

- `GET /api/users` - 获取所有用户
- `GET /api/users/{id}` - 获取指定用户
- `POST /api/users` - 创建新用户
- `PUT /api/users/{id}` - 更新用户
- `DELETE /api/users/{id}` - 删除用户

## 数据库初始化

应用程序启动时会自动：
1. 应用所有待处理的迁移
2. 在开发环境中添加示例数据（如果数据库为空）

这通过 `Program.cs` 中的以下代码实现：

```csharp
await app.EnsureDatabaseCreatedAsync();
if (app.Environment.IsDevelopment())
{
    await app.SeedDatabaseAsync();
}
```

## 注意事项

1. SQLite 数据库文件会在项目根目录下创建
2. 开发环境和生产环境使用不同的数据库文件
3. 迁移文件应该提交到版本控制系统
4. 数据库文件可以添加到 `.gitignore` 中（如果不需要共享数据）
